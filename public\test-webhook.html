<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste de Webhook PIX</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .success {
            background: #28a745;
        }
        .warning {
            background: #ffc107;
            color: black;
        }
        .danger {
            background: #dc3545;
        }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }
    </style>
</head>
<body>
    <h1>🧪 Teste de Webhook PIX</h1>
    
    <div class="container">
        <h2>1. Verificar Status</h2>
        <button onclick="checkStatus()">📊 Verificar Bilhetes e Logs</button>
        <pre id="status-result"></pre>
    </div>

    <div class="container">
        <h2>2. Corrigir Pagamento</h2>
        <button onclick="fixPayment()" class="success">💰 Marcar Bilhete como Pago</button>
        <pre id="fix-result"></pre>
    </div>

    <div class="container">
        <h2>3. Testar Webhook</h2>
        <button onclick="testWebhook()" class="warning">🔔 Simular Webhook PIX</button>
        <pre id="webhook-result"></pre>
    </div>

    <div class="container">
        <h2>4. Verificar Bilhetes Pendentes</h2>
        <button onclick="checkPending()">🎫 Ver Bilhetes Pendentes</button>
        <pre id="pending-result"></pre>
    </div>

    <script>
        async function makeRequest(url, options = {}) {
            try {
                const response = await fetch(url, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });
                const data = await response.json();
                return { success: response.ok, data, status: response.status };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        async function checkStatus() {
            const resultEl = document.getElementById('status-result');
            resultEl.textContent = 'Carregando...';
            
            const result = await makeRequest('/api/admin/fix-payment', {
                method: 'POST',
                body: JSON.stringify({ action: 'check_status' })
            });
            
            resultEl.textContent = JSON.stringify(result, null, 2);
        }

        async function fixPayment() {
            const resultEl = document.getElementById('fix-result');
            resultEl.textContent = 'Atualizando bilhete...';
            
            const result = await makeRequest('/api/admin/fix-payment', {
                method: 'POST',
                body: JSON.stringify({ 
                    action: 'fix_payment',
                    order_id: '5f238dd289044a8bb90ddb98d5e6e418'
                })
            });
            
            resultEl.textContent = JSON.stringify(result, null, 2);
        }

        async function testWebhook() {
            const resultEl = document.getElementById('webhook-result');
            resultEl.textContent = 'Enviando webhook...';
            
            const result = await makeRequest('/api/v1/MP/webhookruntransation', {
                method: 'POST',
                body: JSON.stringify({
                    order_id: '5f238dd289044a8bb90ddb98d5e6e418',
                    status: 'PAID',
                    type: 'PIXOUT',
                    message: 'Payment approved via test page'
                })
            });
            
            resultEl.textContent = JSON.stringify(result, null, 2);
        }

        async function checkPending() {
            const resultEl = document.getElementById('pending-result');
            resultEl.textContent = 'Carregando bilhetes pendentes...';
            
            const result = await makeRequest('/api/debug/bilhetes-pendentes');
            
            resultEl.textContent = JSON.stringify(result, null, 2);
        }

        // Carregar status inicial
        window.onload = function() {
            checkStatus();
        };
    </script>
</body>
</html>
