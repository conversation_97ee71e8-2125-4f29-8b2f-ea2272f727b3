#!/bin/bash

echo "🔍 Buscando bilhetes pendentes..."

# Buscar bilhetes pendentes
echo "📋 Consultando bilhetes pendentes..."
curl -s "http://localhost:3001/api/debug/bilhetes-pendentes" | jq '.'

echo ""
echo "💾 Atualizando bilhete ID 154 para pago..."

# Atualizar bilhete para pago
curl -X POST "http://localhost:3001/api/debug/bilhetes-pendentes" \
  -H "Content-Type: application/json" \
  -d '{
    "bilhete_id": 154,
    "order_id": "5f238dd289044a8bb90ddb98d5e6e418",
    "status": "pago"
  }' | jq '.'

echo ""
echo "🧪 Testando webhook..."

# Testar webhook
curl -X POST "http://localhost:3001/api/v1/MP/webhookruntransation" \
  -H "Content-Type: application/json" \
  -d '{
    "order_id": "5f238dd289044a8bb90ddb98d5e6e418",
    "status": "PAID",
    "type": "PIXOUT",
    "message": "Payment approved via manual fix"
  }' | jq '.'

echo ""
echo "✅ Script concluído!"
