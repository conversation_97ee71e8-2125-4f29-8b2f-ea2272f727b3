// Script para testar o webhook do PIX
import fetch from 'node-fetch'

const WEBHOOK_URL = 'http://localhost:3001/api/v1/MP/webhookruntransation'

async function testWebhook() {
  try {
    console.log('🧪 Testando webhook PIX...')
    
    // Dados do webhook simulado
    const webhookData = {
      order_id: '5f238dd289044a8bb90ddb98d5e6e418',
      status: 'PAID',
      type: 'PIXOUT',
      message: 'Payment approved via test'
    }
    
    console.log('📤 Enviando dados:', webhookData)
    
    const response = await fetch(WEBHOOK_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(webhookData)
    })
    
    const result = await response.json()
    
    console.log('📥 Resposta do webhook:')
    console.log('Status:', response.status)
    console.log('Dados:', JSON.stringify(result, null, 2))
    
    if (result.processed_successfully) {
      console.log('✅ Webhook processado com sucesso!')
    } else {
      console.log('⚠️ Webhook não processou com sucesso')
    }
    
  } catch (error) {
    console.error('❌ Erro ao testar webhook:', error)
  }
}

// Executar teste
testWebhook()
