/**
 * Script para testar a API de bolões e verificar os dados retornados
 */

import { executeQuery, initializeDatabase } from '../lib/database-config.js'

async function testBoloesAPI() {
  try {
    console.log('🧪 Testando API de bolões...')
    
    await initializeDatabase()
    
    // Simular o que a API de bolões faz
    console.log('🔄 Buscando bolões reais...')

    const boloes = await executeQuery(`
      SELECT * FROM boloes
      WHERE status IN ('ativo', 'em_breve')
      ORDER BY id DESC
      LIMIT 10
    `)

    console.log(`📊 Bolões encontrados: ${boloes.length}`)

    if (boloes.length === 0) {
      console.log('❌ Nenhum bolão encontrado!')
      return
    }

    const bolao = boloes[0]
    console.log(`\n🏆 Testando bolão: ${bolao.nome} (ID: ${bolao.id})`)

    // Buscar jogos do bolão
    console.log('🔍 Buscando jogos associados ao bolão...')
    
    // Primeiro tentar buscar da tabela bolao_jogos
    const jogosAssociados = await executeQuery(`
      SELECT
        j.*,
        tc.nome as time_casa_nome,
        tc.nome_curto as time_casa_curto,
        tc.logo_url as time_casa_logo,
        tf.nome as time_fora_nome,
        tf.nome_curto as time_fora_curto,
        tf.logo_url as time_fora_logo,
        c.nome as campeonato_nome,
        c.codigo as campeonato_codigo,
        c.logo_url as campeonato_logo
      FROM bolao_jogos bj
      JOIN jogos j ON bj.jogo_id = j.id
      LEFT JOIN times tc ON j.time_casa_id = tc.id
      LEFT JOIN times tf ON j.time_fora_id = tf.id
      LEFT JOIN campeonatos c ON j.campeonato_id = c.id
      WHERE bj.bolao_id = ?
      ORDER BY j.data_jogo ASC
      LIMIT 5
    `, [bolao.id])

    console.log(`📊 Jogos associados encontrados: ${jogosAssociados.length}`)

    if (jogosAssociados.length > 0) {
      console.log('\n📋 Amostra de jogos associados:')
      jogosAssociados.forEach((jogo, index) => {
        console.log(`\n${index + 1}. Jogo ID: ${jogo.id}`)
        console.log(`   Casa: ${jogo.time_casa_nome || 'SEM NOME'} (${jogo.time_casa_curto || 'SC'})`)
        console.log(`   Fora: ${jogo.time_fora_nome || 'SEM NOME'} (${jogo.time_fora_curto || 'SF'})`)
        console.log(`   Campeonato: ${jogo.campeonato_nome || 'SEM NOME'} (${jogo.campeonato_codigo || 'SC'})`)
        console.log(`   Data: ${jogo.data_jogo}`)
        console.log(`   Status: ${jogo.status}`)
        console.log(`   Logos: Casa=${jogo.time_casa_logo ? 'SIM' : 'NÃO'}, Fora=${jogo.time_fora_logo ? 'SIM' : 'NÃO'}`)
      })
    } else {
      console.log('⚠️ Nenhum jogo associado encontrado. Verificando partidas selecionadas...')
      
      if (bolao.partidas_selecionadas) {
        try {
          const partidasSelecionadas = JSON.parse(bolao.partidas_selecionadas)
          console.log(`📊 Partidas selecionadas: ${partidasSelecionadas.length}`)
          
          if (partidasSelecionadas.length > 0) {
            const partidasIds = partidasSelecionadas.map(p => p.id || p).filter(Boolean)
            console.log(`🔍 IDs das partidas: ${partidasIds.slice(0, 5).join(', ')}...`)
            
            if (partidasIds.length > 0) {
              const placeholders = partidasIds.slice(0, 5).map(() => '?').join(',')
              const jogosPartidas = await executeQuery(`
                SELECT
                  j.*,
                  tc.nome as time_casa_nome,
                  tc.nome_curto as time_casa_curto,
                  tc.logo_url as time_casa_logo,
                  tf.nome as time_fora_nome,
                  tf.nome_curto as time_fora_curto,
                  tf.logo_url as time_fora_logo,
                  c.nome as campeonato_nome,
                  c.codigo as campeonato_codigo
                FROM jogos j
                LEFT JOIN times tc ON j.time_casa_id = tc.id
                LEFT JOIN times tf ON j.time_fora_id = tf.id
                LEFT JOIN campeonatos c ON j.campeonato_id = c.id
                WHERE j.id IN (${placeholders})
                ORDER BY j.data_jogo ASC
              `, partidasIds.slice(0, 5))

              console.log(`📊 Jogos das partidas selecionadas: ${jogosPartidas.length}`)
              
              if (jogosPartidas.length > 0) {
                console.log('\n📋 Amostra de jogos das partidas selecionadas:')
                jogosPartidas.forEach((jogo, index) => {
                  console.log(`\n${index + 1}. Jogo ID: ${jogo.id}`)
                  console.log(`   Casa: ${jogo.time_casa_nome || 'SEM NOME'} (${jogo.time_casa_curto || 'SC'})`)
                  console.log(`   Fora: ${jogo.time_fora_nome || 'SEM NOME'} (${jogo.time_fora_curto || 'SF'})`)
                  console.log(`   Campeonato: ${jogo.campeonato_nome || 'SEM NOME'} (${jogo.campeonato_codigo || 'SC'})`)
                  console.log(`   Data: ${jogo.data_jogo}`)
                  console.log(`   Status: ${jogo.status}`)
                })
              }
            }
          }
        } catch (e) {
          console.log('❌ Erro ao parsear partidas selecionadas:', e.message)
        }
      } else {
        console.log('⚠️ Nenhuma partida selecionada. Verificando campeonatos selecionados...')
        
        if (bolao.campeonatos_selecionados) {
          try {
            const campeonatos = JSON.parse(bolao.campeonatos_selecionados)
            console.log(`📊 Campeonatos selecionados: ${campeonatos.length}`)
            
            if (campeonatos.length > 0) {
              const codigosCampeonatos = campeonatos.map(c => c.codigo).filter(Boolean)
              console.log(`🏆 Códigos dos campeonatos: ${codigosCampeonatos.join(', ')}`)
              
              if (codigosCampeonatos.length > 0) {
                const codigo = codigosCampeonatos[0]
                const jogosCampeonato = await executeQuery(`
                  SELECT
                    j.*,
                    tc.nome as time_casa_nome,
                    tc.nome_curto as time_casa_curto,
                    tc.logo_url as time_casa_logo,
                    tf.nome as time_fora_nome,
                    tf.nome_curto as time_fora_curto,
                    tf.logo_url as time_fora_logo,
                    c.nome as campeonato_nome,
                    c.codigo as campeonato_codigo
                  FROM jogos j
                  LEFT JOIN times tc ON j.time_casa_id = tc.id
                  LEFT JOIN times tf ON j.time_fora_id = tf.id
                  LEFT JOIN campeonatos c ON j.campeonato_id = c.id
                  WHERE c.codigo = ?
                  AND j.status IN ('agendado', 'ao_vivo')
                  AND j.data_jogo >= NOW()
                  ORDER BY j.data_jogo ASC
                  LIMIT 5
                `, [codigo])

                console.log(`📊 Jogos do campeonato ${codigo}: ${jogosCampeonato.length}`)
                
                if (jogosCampeonato.length > 0) {
                  console.log('\n📋 Amostra de jogos do campeonato:')
                  jogosCampeonato.forEach((jogo, index) => {
                    console.log(`\n${index + 1}. Jogo ID: ${jogo.id}`)
                    console.log(`   Casa: ${jogo.time_casa_nome || 'SEM NOME'} (${jogo.time_casa_curto || 'SC'})`)
                    console.log(`   Fora: ${jogo.time_fora_nome || 'SEM NOME'} (${jogo.time_fora_curto || 'SF'})`)
                    console.log(`   Campeonato: ${jogo.campeonato_nome || 'SEM NOME'} (${jogo.campeonato_codigo || 'SC'})`)
                    console.log(`   Data: ${jogo.data_jogo}`)
                    console.log(`   Status: ${jogo.status}`)
                  })
                }
              }
            }
          } catch (e) {
            console.log('❌ Erro ao parsear campeonatos selecionados:', e.message)
          }
        }
      }
    }

    // Testar a formatação final como a API faz
    console.log('\n🔧 Testando formatação final dos dados...')
    
    // Simular o que acontece no mapeamento final
    const jogosParaTeste = jogosAssociados.length > 0 ? jogosAssociados.slice(0, 3) : []
    
    if (jogosParaTeste.length > 0) {
      console.log('\n📋 Dados formatados como a API retorna:')
      
      const jogosFormatados = jogosParaTeste.map((jogo) => ({
        id: jogo.id,
        time_casa: jogo.time_casa_nome || jogo.time_casa,
        time_fora: jogo.time_fora_nome || jogo.time_fora,
        time_casa_logo: jogo.time_casa_logo,
        time_fora_logo: jogo.time_fora_logo,
        data_jogo: jogo.data_jogo,
        campeonato: jogo.campeonato_nome,
        campeonato_codigo: jogo.campeonato_codigo,
        status: jogo.status,
        resultado_casa: jogo.resultado_casa,
        resultado_fora: jogo.resultado_fora
      }))
      
      jogosFormatados.forEach((jogo, index) => {
        console.log(`\n${index + 1}. Jogo formatado:`)
        console.log(`   ID: ${jogo.id}`)
        console.log(`   time_casa: ${jogo.time_casa || 'UNDEFINED'}`)
        console.log(`   time_fora: ${jogo.time_fora || 'UNDEFINED'}`)
        console.log(`   time_casa_logo: ${jogo.time_casa_logo || 'UNDEFINED'}`)
        console.log(`   time_fora_logo: ${jogo.time_fora_logo || 'UNDEFINED'}`)
        console.log(`   campeonato: ${jogo.campeonato || 'UNDEFINED'}`)
        console.log(`   campeonato_codigo: ${jogo.campeonato_codigo || 'UNDEFINED'}`)
      })
    }

    console.log('\n✅ Teste da API concluído!')
    
  } catch (error) {
    console.error('❌ Erro no teste:', error)
  }
}

// Executar o teste
testBoloesAPI()
  .then(() => {
    console.log('🎯 Teste concluído!')
    process.exit(0)
  })
  .catch(error => {
    console.error('❌ Erro fatal:', error)
    process.exit(1)
  })
