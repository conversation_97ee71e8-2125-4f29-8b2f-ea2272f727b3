<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste SweetAlert2 - Pagamento</title>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        button {
            background: #10B981;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
        }
        button:hover {
            background: #059669;
        }
        .webhook-btn {
            background: #3B82F6;
        }
        .webhook-btn:hover {
            background: #2563EB;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎉 Teste SweetAlert2 - Sistema de Pagamento</h1>
        <p>Clique nos botões abaixo para testar os alertas de pagamento:</p>
        
        <button onclick="testPaymentSuccess()">
            ✅ Simular Pagamento Aprovado
        </button>
        
        <button onclick="testPaymentError()">
            ❌ Simular Erro no Pagamento
        </button>
        
        <button onclick="testWebhook()" class="webhook-btn">
            📡 Simular Webhook de Pagamento
        </button>
        
        <hr style="margin: 30px 0;">
        
        <h2>📋 Dados de Teste</h2>
        <p><strong>Código do Bilhete:</strong> BLT175315169460129O6UQ5E</p>
        <p><strong>Valor:</strong> R$ 0,09</p>
        <p><strong>Cliente:</strong> Sapao</p>
        <p><strong>Transaction ID:</strong> pixi_01k0qzqrg2ehv9w7c8ffhp9afk</p>
    </div>

    <script>
        // Configuração padrão do SweetAlert2
        const defaultConfig = {
            customClass: {
                popup: 'rounded-2xl shadow-2xl',
                title: 'text-2xl font-bold',
                content: 'text-lg',
                confirmButton: 'bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-6 rounded-lg transition-colors duration-200',
                cancelButton: 'bg-gray-500 hover:bg-gray-600 text-white font-bold py-3 px-6 rounded-lg transition-colors duration-200'
            },
            buttonsStyling: false,
            allowOutsideClick: false,
            allowEscapeKey: false
        }

        // Função para mostrar alerta de sucesso de pagamento
        function showPaymentSuccessAlert(data) {
            const valorFormatado = typeof data.valor === 'number' 
                ? data.valor.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' })
                : `R$ ${data.valor}`

            return Swal.fire({
                ...defaultConfig,
                icon: 'success',
                title: '🎉 Pagamento Confirmado!',
                html: `
                    <div style="text-align: center; margin: 20px 0;">
                        <div style="background: #dcfce7; border: 2px solid #16a34a; border-radius: 12px; padding: 20px; margin-bottom: 20px;">
                            <h3 style="color: #15803d; margin-bottom: 15px; font-size: 18px;">✅ Bilhete Aprovado</h3>
                            <p style="color: #166534; margin: 8px 0;"><strong>Código:</strong> ${data.codigo}</p>
                            <p style="color: #166534; margin: 8px 0;"><strong>Valor:</strong> ${valorFormatado}</p>
                            ${data.clientName ? `<p style="color: #166534; margin: 8px 0;"><strong>Cliente:</strong> ${data.clientName}</p>` : ''}
                            ${data.transactionId ? `<p style="color: #059669; margin-top: 15px; font-size: 12px;">ID: ${data.transactionId}</p>` : ''}
                        </div>
                        <div style="color: #6b7280;">
                            <p>✅ Pagamento processado com sucesso</p>
                            <p>📱 Você pode acompanhar seus bilhetes na área do usuário</p>
                        </div>
                    </div>
                `,
                confirmButtonText: 'Entendi!',
                timer: 10000,
                timerProgressBar: true,
                width: '500px'
            })
        }

        // Função para mostrar alerta de erro
        function showPaymentErrorAlert(message = 'Erro no processamento do pagamento') {
            return Swal.fire({
                ...defaultConfig,
                icon: 'error',
                title: '❌ Erro no Pagamento',
                text: message,
                confirmButtonText: 'Tentar Novamente',
                customClass: {
                    ...defaultConfig.customClass,
                    confirmButton: 'bg-red-600 hover:bg-red-700 text-white font-bold py-3 px-6 rounded-lg transition-colors duration-200'
                }
            })
        }

        // Testes
        function testPaymentSuccess() {
            showPaymentSuccessAlert({
                codigo: 'BLT175315169460129O6UQ5E',
                valor: '0,09',
                clientName: 'Sapao',
                transactionId: 'pixi_01k0qzqrg2ehv9w7c8ffhp9afk'
            })
        }

        function testPaymentError() {
            showPaymentErrorAlert('Falha na comunicação com o gateway de pagamento. Tente novamente.')
        }

        function testWebhook() {
            // Simular recebimento de webhook
            Swal.fire({
                title: '📡 Simulando Webhook...',
                text: 'Processando pagamento via webhook',
                icon: 'info',
                timer: 2000,
                showConfirmButton: false,
                timerProgressBar: true
            }).then(() => {
                // Após 2 segundos, mostrar sucesso
                testPaymentSuccess()
            })
        }

        // Mostrar alerta de boas-vindas
        window.addEventListener('load', () => {
            Swal.fire({
                title: '🎯 Sistema de Teste',
                text: 'SweetAlert2 carregado com sucesso! Teste os alertas de pagamento.',
                icon: 'info',
                confirmButtonText: 'Começar Teste',
                ...defaultConfig
            })
        })
    </script>
</body>
</html>
