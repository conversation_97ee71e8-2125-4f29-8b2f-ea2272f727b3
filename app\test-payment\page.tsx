"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { showPaymentSuccessAlert, showPaymentErrorAlert, showSuccessToast } from "@/lib/sweetalert"

export default function TestPaymentPage() {
  const [loading, setLoading] = useState(false)

  const testPaymentSuccess = async () => {
    setLoading(true)
    try {
      // Simular dados de pagamento
      const paymentData = {
        codigo: "BLT175315169460129O6UQ5E",
        valor: "0,09",
        clientName: "Sapao (Teste)",
        transactionId: "pixi_01k0qzqrg2ehv9w7c8ffhp9afk"
      }

      // Mostrar SweetAlert2
      await showPaymentSuccessAlert(paymentData)
      
      // Mostrar toast adicional
      showSuccessToast("Teste de pagamento executado com sucesso!")
      
    } catch (error) {
      console.error("Erro no teste:", error)
      showPaymentErrorAlert("Erro ao executar teste de pagamento")
    } finally {
      setLoading(false)
    }
  }

  const testPaymentError = async () => {
    setLoading(true)
    try {
      await showPaymentErrorAlert("Falha na comunicação com o gateway de pagamento. Tente novamente.")
    } finally {
      setLoading(false)
    }
  }

  const testWebhookSimulation = async () => {
    setLoading(true)
    try {
      // Simular chamada para o webhook de teste
      const response = await fetch('/api/test/payment-success', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          bilhete_codigo: "BLT_WEBHOOK_TEST_" + Date.now(),
          valor: "0,09",
          client_name: "Cliente Webhook Teste",
          transaction_id: "webhook_test_" + Date.now()
        })
      })

      const result = await response.json()
      
      if (result.success) {
        await showPaymentSuccessAlert({
          codigo: result.data.codigo,
          valor: result.data.valor,
          clientName: result.data.client_name,
          transactionId: result.data.transaction_id
        })
      } else {
        await showPaymentErrorAlert("Erro na simulação do webhook")
      }
      
    } catch (error) {
      console.error("Erro no teste de webhook:", error)
      showPaymentErrorAlert("Erro ao testar webhook")
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <Card>
          <CardHeader>
            <CardTitle className="text-2xl font-bold text-center">
              🧪 Teste SweetAlert2 - Sistema de Pagamento
            </CardTitle>
            <p className="text-center text-gray-600">
              Teste os alertas de pagamento do sistema
            </p>
          </CardHeader>
          <CardContent className="space-y-6">
            
            {/* Botões de Teste */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Button 
                onClick={testPaymentSuccess}
                disabled={loading}
                className="bg-green-600 hover:bg-green-700 text-white py-6"
              >
                ✅ Pagamento Aprovado
              </Button>
              
              <Button 
                onClick={testPaymentError}
                disabled={loading}
                variant="destructive"
                className="py-6"
              >
                ❌ Erro no Pagamento
              </Button>
              
              <Button 
                onClick={testWebhookSimulation}
                disabled={loading}
                className="bg-blue-600 hover:bg-blue-700 text-white py-6"
              >
                📡 Simular Webhook
              </Button>
            </div>

            {/* Dados de Teste */}
            <Card className="bg-gray-50">
              <CardHeader>
                <CardTitle className="text-lg">📋 Dados de Teste</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <p><strong>Código do Bilhete:</strong> BLT175315169460129O6UQ5E</p>
                <p><strong>Valor:</strong> R$ 0,09</p>
                <p><strong>Cliente:</strong> Sapao</p>
                <p><strong>Transaction ID:</strong> pixi_01k0qzqrg2ehv9w7c8ffhp9afk</p>
              </CardContent>
            </Card>

            {/* Instruções */}
            <Card className="bg-blue-50 border-blue-200">
              <CardHeader>
                <CardTitle className="text-lg text-blue-800">📖 Instruções</CardTitle>
              </CardHeader>
              <CardContent className="text-blue-700">
                <ol className="list-decimal list-inside space-y-2">
                  <li>Clique em "Pagamento Aprovado" para testar o alerta de sucesso</li>
                  <li>Clique em "Erro no Pagamento" para testar o alerta de erro</li>
                  <li>Clique em "Simular Webhook" para testar o fluxo completo</li>
                  <li>Os alertas devem aparecer com animações e estilos personalizados</li>
                </ol>
              </CardContent>
            </Card>

            {/* Voltar */}
            <div className="text-center">
              <Button 
                onClick={() => window.history.back()}
                variant="outline"
              >
                ← Voltar
              </Button>
            </div>

          </CardContent>
        </Card>
      </div>
    </div>
  )
}
