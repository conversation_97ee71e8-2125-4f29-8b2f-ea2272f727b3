import { NextRequest, NextResponse } from "next/server"

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { bilhete_codigo, valor, client_name, transaction_id } = body

    console.log("🧪 Teste de pagamento aprovado:", { bilhete_codigo, valor, client_name, transaction_id })

    // Simular resposta de pagamento aprovado
    return NextResponse.json({
      success: true,
      message: "Pagamento de teste aprovado com sucesso!",
      data: {
        codigo: bilhete_codigo || "BLT_TESTE_" + Date.now(),
        valor: valor || "0,09",
        client_name: client_name || "Cliente Teste",
        transaction_id: transaction_id || "test_" + Date.now(),
        status: "pago",
        timestamp: new Date().toISOString(),
        show_success_alert: true
      }
    })

  } catch (error) {
    console.error("❌ Erro no teste de pagamento:", error)
    return NextResponse.json({
      success: false,
      error: "Erro no teste de pagamento"
    }, { status: 500 })
  }
}

export async function GET() {
  return NextResponse.json({
    message: "Endpoint de teste para pagamento aprovado",
    usage: "POST com dados: { bilhete_codigo, valor, client_name, transaction_id }",
    example: {
      bilhete_codigo: "BLT175315169460129O6UQ5E",
      valor: "0,09",
      client_name: "Sapao",
      transaction_id: "pixi_01k0qzqrg2ehv9w7c8ffhp9afk"
    }
  })
}
