import { NextRequest, NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

export async function GET(request: NextRequest) {
  try {
    await initializeDatabase()

    // Buscar bilhetes pendentes recentes
    const bilhetesPendentes = await executeQuery(`
      SELECT 
        id, codigo, usuario_id, status, transaction_id, pix_order_id, 
        valor_total, usuario_nome, usuario_email, created_at, updated_at
      FROM bilhetes 
      WHERE status = 'pendente'
      ORDER BY created_at DESC 
      LIMIT 20
    `)

    console.log("🔍 Debug - Bilhetes pendentes encontrados:", bilhetesPendentes)

    // Buscar também logs de webhook recentes
    const webhookLogs = await executeQuery(`
      SELECT 
        id, order_id, transaction_id, status, webhook_data, processed_at
      FROM webhook_logs 
      ORDER BY processed_at DESC 
      LIMIT 10
    `)

    console.log("📋 Debug - Logs de webhook recentes:", webhookLogs)

    return NextResponse.json({
      success: true,
      bilhetes_pendentes: bilhetesPendentes,
      total_pendentes: Array.isArray(bilhetesPendentes) ? bilhetesPendentes.length : 0,
      webhook_logs: webhookLogs,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error("❌ Erro no debug de bilhetes pendentes:", error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : "Erro desconhecido"
    }, { status: 500 })
  }
}

// Endpoint POST para forçar atualização de um bilhete específico
export async function POST(request: NextRequest) {
  try {
    const { bilhete_id, order_id, status = 'pago' } = await request.json()

    if (!bilhete_id && !order_id) {
      return NextResponse.json({
        success: false,
        error: "bilhete_id ou order_id é obrigatório"
      }, { status: 400 })
    }

    await initializeDatabase()

    let updateResult
    if (bilhete_id) {
      // Atualizar por ID
      updateResult = await executeQuery(`
        UPDATE bilhetes 
        SET status = ?, pix_order_id = COALESCE(pix_order_id, ?), updated_at = NOW()
        WHERE id = ?
      `, [status, order_id || null, bilhete_id])
    } else {
      // Atualizar por order_id
      updateResult = await executeQuery(`
        UPDATE bilhetes 
        SET status = ?, pix_order_id = ?, updated_at = NOW()
        WHERE transaction_id = ? OR codigo = ? OR pix_order_id = ?
      `, [status, order_id, order_id, order_id, order_id])
    }

    console.log("✅ Resultado da atualização manual:", updateResult)

    // Buscar bilhete atualizado
    const bilheteAtualizado = await executeQuery(`
      SELECT * FROM bilhetes 
      WHERE id = ? OR transaction_id = ? OR codigo = ? OR pix_order_id = ?
      LIMIT 1
    `, [bilhete_id || 0, order_id || '', order_id || '', order_id || ''])

    return NextResponse.json({
      success: true,
      message: "Bilhete atualizado manualmente",
      linhas_afetadas: (updateResult as any)?.affectedRows || 0,
      bilhete_atualizado: bilheteAtualizado[0] || null
    })

  } catch (error) {
    console.error("❌ Erro ao atualizar bilhete manualmente:", error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : "Erro desconhecido"
    }, { status: 500 })
  }
}
