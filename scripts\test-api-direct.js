/**
 * <PERSON>ript para testar a API diretamente via HTTP
 */

async function testAPI() {
  try {
    console.log('🌐 Testando API de bolões via HTTP...')
    
    const response = await fetch('http://localhost:3000/api/boloes', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    })
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
    
    const data = await response.json()
    
    console.log('📊 Resposta da API:')
    console.log(`  Success: ${data.success}`)
    console.log(`  Bolões: ${data.boloes?.length || 0}`)
    
    if (data.boloes && data.boloes.length > 0) {
      const bolao = data.boloes[0]
      console.log(`\n🏆 Primeiro bolão:`)
      console.log(`  ID: ${bolao.id}`)
      console.log(`  Nome: ${bolao.nome}`)
      console.log(`  Jogos: ${bolao.jogos?.length || 0}`)
      
      if (bolao.jogos && bolao.jogos.length > 0) {
        console.log(`\n📋 Primeiros 3 jogos:`)
        bolao.jogos.slice(0, 3).forEach((jogo, index) => {
          console.log(`\n  ${index + 1}. Jogo ID: ${jogo.id}`)
          console.log(`     Casa: ${jogo.time_casa}`)
          console.log(`     Fora: ${jogo.time_fora}`)
          console.log(`     Campeonato: ${jogo.campeonato}`)
          console.log(`     Código: ${jogo.campeonato_codigo}`)
          console.log(`     Data: ${jogo.data_jogo}`)
        })
      } else {
        console.log('❌ Nenhum jogo encontrado no bolão!')
      }
    } else {
      console.log('❌ Nenhum bolão encontrado!')
      if (data.error) {
        console.log(`   Erro: ${data.error}`)
        console.log(`   Mensagem: ${data.message}`)
      }
    }
    
  } catch (error) {
    console.error('❌ Erro ao testar API:', error.message)
  }
}

// Executar o teste
testAPI()
  .then(() => {
    console.log('\n✅ Teste concluído!')
    process.exit(0)
  })
  .catch(error => {
    console.error('❌ Erro fatal:', error)
    process.exit(1)
  })
