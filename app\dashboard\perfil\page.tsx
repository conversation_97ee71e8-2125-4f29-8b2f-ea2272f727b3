"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Calendar,
  Shield,
  Key,
  Save,
  Loader2
} from "lucide-react"
import { toast } from "sonner"

interface UserProfile {
  id: number
  nome: string
  email: string
  telefone: string
  endereco: string
  cpf: string
  data_nascimento: string
  data_cadastro: string
  status: string
  tipo: string
}

export default function PerfilPage() {
  const [profile, setProfile] = useState<UserProfile | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [formData, setFormData] = useState({
    nome: "",
    telefone: "",
    endereco: "",
    senha_atual: "",
    nova_senha: "",
    confirmar_senha: ""
  })

  useEffect(() => {
    loadProfile()
  }, [])

  const loadProfile = async () => {
    try {
      setLoading(true)

      // Obter dados do usuário do localStorage
      const userData = localStorage.getItem("user")
      const userId = userData ? JSON.parse(userData).id : 1

      // Buscar dados reais do perfil
      const response = await fetch(`/api/dashboard/perfil?userId=${userId}`)
      if (!response.ok) {
        throw new Error('Erro ao carregar perfil')
      }

      const profileData = await response.json()

      setProfile(profileData)
      setFormData({
        nome: profileData.nome || "",
        telefone: profileData.telefone || "",
        endereco: profileData.endereco || "",
        senha_atual: "",
        nova_senha: "",
        confirmar_senha: ""
      })
    } catch (error) {
      console.error("Erro ao carregar perfil:", error)
      toast.error("Erro ao carregar perfil")
    } finally {
      setLoading(false)
    }
  }

  const handleSaveProfile = async () => {
    try {
      setSaving(true)

      // Obter dados do usuário do localStorage
      const userData = localStorage.getItem("user")
      const userId = userData ? JSON.parse(userData).id : 1

      // Enviar dados para API
      const response = await fetch('/api/dashboard/perfil', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          nome: formData.nome,
          telefone: formData.telefone,
          endereco: formData.endereco
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Erro ao salvar perfil')
      }

      // Atualizar perfil local
      if (profile) {
        const updatedProfile = {
          ...profile,
          nome: formData.nome,
          telefone: formData.telefone,
          endereco: formData.endereco
        }
        setProfile(updatedProfile)

        // Atualizar localStorage
        if (userData) {
          const user = JSON.parse(userData)
          user.nome = formData.nome
          localStorage.setItem("user", JSON.stringify(user))
        }
      }

      toast.success("Perfil atualizado com sucesso!")
    } catch (error: any) {
      console.error("Erro ao salvar perfil:", error)
      toast.error(error.message || "Erro ao salvar perfil")
    } finally {
      setSaving(false)
    }
  }

  const handleChangePassword = async () => {
    try {
      if (!formData.senha_atual || !formData.nova_senha || !formData.confirmar_senha) {
        toast.error("Preencha todos os campos de senha")
        return
      }

      if (formData.nova_senha !== formData.confirmar_senha) {
        toast.error("A nova senha e confirmação não coincidem")
        return
      }

      if (formData.nova_senha.length < 6) {
        toast.error("A nova senha deve ter pelo menos 6 caracteres")
        return
      }

      setSaving(true)
      
      // Simular alteração de senha
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      setFormData({
        ...formData,
        senha_atual: "",
        nova_senha: "",
        confirmar_senha: ""
      })
      
      toast.success("Senha alterada com sucesso!")
    } catch (error) {
      console.error("Erro ao alterar senha:", error)
      toast.error("Erro ao alterar senha")
    } finally {
      setSaving(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("pt-BR")
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "ativo":
        return <Badge className="bg-green-100 text-green-800">Ativo</Badge>
      case "inativo":
        return <Badge variant="secondary">Inativo</Badge>
      case "bloqueado":
        return <Badge variant="destructive">Bloqueado</Badge>
      default:
        return <Badge variant="outline">Desconhecido</Badge>
    }
  }

  const getTipoBadge = (tipo: string) => {
    switch (tipo) {
      case "admin":
        return <Badge className="bg-purple-100 text-purple-800">Admin</Badge>
      case "cambista":
        return <Badge className="bg-blue-100 text-blue-800">Cambista</Badge>
      case "usuario":
        return <Badge variant="outline">Usuário</Badge>
      default:
        return <Badge variant="outline">Desconhecido</Badge>
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-gray-600">Carregando perfil...</p>
        </div>
      </div>
    )
  }

  if (!profile) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-600">Erro ao carregar perfil</p>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Meu Perfil</h1>
        <p className="text-gray-600 mt-2">Gerencie suas informações pessoais e configurações</p>
      </div>

      {/* Profile Overview */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center space-x-6">
            <div className="bg-green-100 p-6 rounded-full">
              <User className="h-12 w-12 text-green-600" />
            </div>
            <div className="flex-1">
              <h2 className="text-2xl font-bold text-gray-900">{profile.nome}</h2>
              <p className="text-gray-600">{profile.email}</p>
              <div className="flex items-center space-x-4 mt-2">
                {getStatusBadge(profile.status)}
                {getTipoBadge(profile.tipo)}
                <Badge variant="outline">
                  <Calendar className="h-3 w-3 mr-1" />
                  Membro desde {formatDate(profile.data_cadastro)}
                </Badge>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tabs */}
      <Tabs defaultValue="dados" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="dados">Dados Pessoais</TabsTrigger>
          <TabsTrigger value="seguranca">Segurança</TabsTrigger>
          <TabsTrigger value="informacoes">Informações da Conta</TabsTrigger>
        </TabsList>

        {/* Dados Pessoais Tab */}
        <TabsContent value="dados">
          <Card>
            <CardHeader>
              <CardTitle>Dados Pessoais</CardTitle>
              <CardDescription>Atualize suas informações pessoais</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="nome">Nome Completo</Label>
                  <Input
                    id="nome"
                    value={formData.nome}
                    onChange={(e) => setFormData({ ...formData, nome: e.target.value })}
                    placeholder="Seu nome completo"
                  />
                </div>
                <div>
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    value={profile.email}
                    disabled
                    className="bg-gray-50"
                  />
                  <p className="text-xs text-gray-500 mt-1">O email não pode ser alterado</p>
                </div>
                <div>
                  <Label htmlFor="telefone">Telefone</Label>
                  <Input
                    id="telefone"
                    value={formData.telefone}
                    onChange={(e) => setFormData({ ...formData, telefone: e.target.value })}
                    placeholder="(11) 99999-9999"
                  />
                </div>
                <div>
                  <Label htmlFor="cpf">CPF</Label>
                  <Input
                    id="cpf"
                    value={profile.cpf}
                    disabled
                    className="bg-gray-50"
                  />
                  <p className="text-xs text-gray-500 mt-1">O CPF não pode ser alterado</p>
                </div>
              </div>
              
              <div>
                <Label htmlFor="endereco">Endereço</Label>
                <Input
                  id="endereco"
                  value={formData.endereco}
                  onChange={(e) => setFormData({ ...formData, endereco: e.target.value })}
                  placeholder="Seu endereço completo"
                />
              </div>

              <div className="flex justify-end">
                <Button onClick={handleSaveProfile} disabled={saving}>
                  {saving ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Save className="h-4 w-4 mr-2" />
                  )}
                  Salvar Alterações
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Segurança Tab */}
        <TabsContent value="seguranca">
          <Card>
            <CardHeader>
              <CardTitle>Segurança</CardTitle>
              <CardDescription>Altere sua senha de acesso</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <Label htmlFor="senha_atual">Senha Atual</Label>
                <Input
                  id="senha_atual"
                  type="password"
                  value={formData.senha_atual}
                  onChange={(e) => setFormData({ ...formData, senha_atual: e.target.value })}
                  placeholder="Digite sua senha atual"
                />
              </div>
              
              <div>
                <Label htmlFor="nova_senha">Nova Senha</Label>
                <Input
                  id="nova_senha"
                  type="password"
                  value={formData.nova_senha}
                  onChange={(e) => setFormData({ ...formData, nova_senha: e.target.value })}
                  placeholder="Digite a nova senha"
                />
              </div>
              
              <div>
                <Label htmlFor="confirmar_senha">Confirmar Nova Senha</Label>
                <Input
                  id="confirmar_senha"
                  type="password"
                  value={formData.confirmar_senha}
                  onChange={(e) => setFormData({ ...formData, confirmar_senha: e.target.value })}
                  placeholder="Confirme a nova senha"
                />
              </div>

              <div className="flex justify-end">
                <Button onClick={handleChangePassword} disabled={saving}>
                  {saving ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Key className="h-4 w-4 mr-2" />
                  )}
                  Alterar Senha
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Informações da Conta Tab */}
        <TabsContent value="informacoes">
          <Card>
            <CardHeader>
              <CardTitle>Informações da Conta</CardTitle>
              <CardDescription>Detalhes da sua conta no sistema</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <User className="h-5 w-5 text-gray-400" />
                    <div>
                      <p className="text-sm font-medium text-gray-600">ID da Conta</p>
                      <p className="text-lg">{profile.id}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-3">
                    <Mail className="h-5 w-5 text-gray-400" />
                    <div>
                      <p className="text-sm font-medium text-gray-600">Email</p>
                      <p className="text-lg">{profile.email}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-3">
                    <Calendar className="h-5 w-5 text-gray-400" />
                    <div>
                      <p className="text-sm font-medium text-gray-600">Data de Cadastro</p>
                      <p className="text-lg">{formatDate(profile.data_cadastro)}</p>
                    </div>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <Shield className="h-5 w-5 text-gray-400" />
                    <div>
                      <p className="text-sm font-medium text-gray-600">Status da Conta</p>
                      <div className="mt-1">{getStatusBadge(profile.status)}</div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-3">
                    <User className="h-5 w-5 text-gray-400" />
                    <div>
                      <p className="text-sm font-medium text-gray-600">Tipo de Conta</p>
                      <div className="mt-1">{getTipoBadge(profile.tipo)}</div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-3">
                    <Calendar className="h-5 w-5 text-gray-400" />
                    <div>
                      <p className="text-sm font-medium text-gray-600">Data de Nascimento</p>
                      <p className="text-lg">{formatDate(profile.data_nascimento)}</p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
